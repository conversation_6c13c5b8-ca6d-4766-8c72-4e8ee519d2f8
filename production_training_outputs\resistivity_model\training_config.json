{"learning_rate": 0.0001, "weight_decay": 3e-05, "batch_size": 8, "max_epochs": 150, "patience": 60, "optimizer": "adamw", "scheduler": "plateau", "scheduler_params": {"patience": 30, "factor": 0.4, "min_lr": 1e-08}, "loss_config": {"type": "curve_specific", "custom_params": {"loss_type": "mae", "constraint_weight": 0.2, "physics_constraints": true}}, "save_best_only": true, "save_frequency": 15, "validation_frequency": 1, "gradient_clipping": 1.5, "mixed_precision": false, "description": "Optimized configuration for resistivity (RLLD) prediction", "target_curve": "RLLD", "save_dir": "production_training_outputs\\resistivity_model"}