{"learning_rate": 5e-05, "weight_decay": 1e-05, "batch_size": 16, "max_epochs": 100, "patience": 30, "optimizer": "adam", "scheduler": "plateau", "scheduler_params": {"patience": 20, "factor": 0.6, "min_lr": 1e-07}, "loss_config": {"type": "curve_specific", "custom_params": {"constraint_weight": 0.5, "physics_constraints": true}}, "save_best_only": true, "save_frequency": 10, "validation_frequency": 1, "gradient_clipping": 0.8, "mixed_precision": false, "description": "Optimized configuration for bulk density (DEN) prediction", "target_curve": "DEN", "save_dir": "production_training_outputs\\density_model"}